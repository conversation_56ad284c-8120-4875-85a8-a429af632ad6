<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>744</width>
    <height>712</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_5">
    <item row="0" column="0">
     <widget class="QGroupBox" name="groupBox">
      <property name="title">
       <string>校准设备</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QWidget" name="widget" native="true">
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <layout class="QGridLayout" name="gridLayout_2" rowstretch="0,0,0,0" columnstretch="1,1,1,1">
            <property name="sizeConstraint">
             <enum>QLayout::SetDefaultConstraint</enum>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <property name="horizontalSpacing">
             <number>6</number>
            </property>
            <item row="2" column="1">
             <widget class="QStackedWidget" name="addressStack_Cal">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="currentIndex">
               <number>0</number>
              </property>
              <widget class="QWidget" name="page">
               <layout class="QGridLayout" name="gridLayout_18">
                <item row="0" column="0">
                 <widget class="QComboBox" name="portNum_Cal">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>88</width>
                    <height>0</height>
                   </size>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="page_2">
               <layout class="QGridLayout" name="gridLayout_20">
                <item row="0" column="0">
                 <widget class="QLineEdit" name="ipAddress_Cal">
                  <property name="minimumSize">
                   <size>
                    <width>95</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="placeholderText">
                   <string>请输入IP地址</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QComboBox" name="communicationType_Cal">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>设备型号：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_4">
              <property name="text">
               <string>通讯方式：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="3">
             <widget class="QComboBox" name="deviceName_Cal">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="1" column="3">
             <widget class="QComboBox" name="chNums_Cal"/>
            </item>
            <item row="1" column="2">
             <widget class="QLabel" name="label_7">
              <property name="text">
               <string>通道个数：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QLabel" name="rateLabel_Cal">
              <property name="text">
               <string>波特率：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="2" column="3">
             <widget class="QStackedWidget" name="rateStack_Cal">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="lineWidth">
               <number>1</number>
              </property>
              <property name="currentIndex">
               <number>0</number>
              </property>
              <widget class="QWidget" name="page_3">
               <layout class="QGridLayout" name="gridLayout_13">
                <item row="0" column="0">
                 <widget class="QComboBox" name="baudNum_Cal">
                  <property name="minimumSize">
                   <size>
                    <width>88</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="page_4">
               <layout class="QGridLayout" name="gridLayout_22">
                <item row="0" column="0">
                 <widget class="QLineEdit" name="ipPort_Cal">
                  <property name="minimumSize">
                   <size>
                    <width>95</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="placeholderText">
                   <string>请输入端口号</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QLabel" name="label_2">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>设备名称：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="addressLabel_Cal">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>串口号：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QComboBox" name="deviceModel_Cal">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QPushButton" name="connectButton_Cal">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>打开</string>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QPushButton" name="disconnectButton_Cal">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>关闭</string>
              </property>
             </widget>
            </item>
            <item row="3" column="2" colspan="2">
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="0" column="1" rowspan="2">
     <widget class="QWidget" name="widget_3" native="true">
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="1" column="1">
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <widget class="QPushButton" name="startCalibration">
           <property name="text">
            <string>开始标定</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="startAdjustment">
           <property name="text">
            <string>开始校准</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="0" column="1">
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>483</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="1" column="0">
        <spacer name="horizontalSpacer_6">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>101</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QGroupBox" name="groupBox_3">
      <property name="title">
       <string>1220转换开关</string>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <widget class="QWidget" name="widget_2" native="true">
         <layout class="QVBoxLayout" name="verticalLayout">
          <item>
           <layout class="QGridLayout" name="gridLayout" columnstretch="1,1,1,1">
            <property name="bottomMargin">
             <number>6</number>
            </property>
            <item row="0" column="3">
             <widget class="QLineEdit" name="serialNum_1220">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QLabel" name="rateLabel_1220">
              <property name="text">
               <string>波特率：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QComboBox" name="deviceModel_1220"/>
            </item>
            <item row="3" column="1">
             <widget class="QPushButton" name="disconnectButton_1220">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>关闭</string>
              </property>
             </widget>
            </item>
            <item row="2" column="3">
             <widget class="QStackedWidget" name="rateStack_1220">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="lineWidth">
               <number>1</number>
              </property>
              <property name="currentIndex">
               <number>0</number>
              </property>
              <widget class="QWidget" name="page_7">
               <layout class="QGridLayout" name="gridLayout_14">
                <item row="0" column="0">
                 <widget class="QComboBox" name="baudNum_1220">
                  <property name="minimumSize">
                   <size>
                    <width>88</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="page_8">
               <layout class="QGridLayout" name="gridLayout_23">
                <item row="0" column="0">
                 <widget class="QLineEdit" name="ipPort_1220">
                  <property name="minimumSize">
                   <size>
                    <width>95</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="placeholderText">
                   <string>请输入端口号</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QStackedWidget" name="addressStack_1220">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="currentIndex">
               <number>0</number>
              </property>
              <widget class="QWidget" name="page_5">
               <layout class="QGridLayout" name="gridLayout_19">
                <item row="0" column="0">
                 <widget class="QComboBox" name="portNum_1220">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>88</width>
                    <height>0</height>
                   </size>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="page_6">
               <layout class="QGridLayout" name="gridLayout_21">
                <item row="0" column="0">
                 <widget class="QLineEdit" name="ipAddress_1220">
                  <property name="minimumSize">
                   <size>
                    <width>95</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="placeholderText">
                   <string>请输入IP地址</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QComboBox" name="communicationType_1220">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_5">
              <property name="text">
               <string>通讯方式：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="2">
             <widget class="QLabel" name="label_55">
              <property name="text">
               <string>校准日期：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QPushButton" name="connectButton_1220">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>打开</string>
              </property>
             </widget>
            </item>
            <item row="1" column="3">
             <widget class="QDateTimeEdit" name="calDate_1220">
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
              </property>
              <property name="dateTime">
               <datetime>
                <hour>0</hour>
                <minute>0</minute>
                <second>0</second>
                <year>2024</year>
                <month>1</month>
                <day>1</day>
               </datetime>
              </property>
              <property name="displayFormat">
               <string>yyyy/MM/dd</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="addressLabel_1220">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>串口号：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QLabel" name="label_28">
              <property name="text">
               <string>序列号：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_25">
              <property name="text">
               <string>设备型号：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="3" column="2">
             <widget class="QLabel" name="label_6">
              <property name="text">
               <string>等待延迟：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="3" column="3">
             <widget class="QComboBox" name="switchDelay"/>
            </item>
           </layout>
          </item>
          <item>
           <widget class="Line" name="line">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QGridLayout" name="gridLayout_4" columnstretch="1,1,1,1,1,1">
            <item row="7" column="0">
             <widget class="QLabel" name="label_16">
              <property name="text">
               <string>挡位：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QComboBox" name="referGear2"/>
            </item>
            <item row="0" column="0">
             <widget class="QCheckBox" name="refer1">
              <property name="text">
               <string>参考1</string>
              </property>
             </widget>
            </item>
            <item row="8" column="0">
             <widget class="QCheckBox" name="refer5">
              <property name="text">
               <string>参考5</string>
              </property>
             </widget>
            </item>
            <item row="1" column="5">
             <widget class="QComboBox" name="referCH1"/>
            </item>
            <item row="8" column="1">
             <spacer name="horizontalSpacer_8">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="7" column="2">
             <widget class="QLabel" name="label_17">
              <property name="text">
               <string>阻值（Ω）：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="7" column="5">
             <widget class="QComboBox" name="referCH4"/>
            </item>
            <item row="3" column="2">
             <widget class="QLabel" name="label_12">
              <property name="text">
               <string>阻值（Ω）：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="2">
             <widget class="QLabel" name="label_8">
              <property name="text">
               <string>阻值（Ω）：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="6" column="0">
             <widget class="QCheckBox" name="refer4">
              <property name="text">
               <string>参考4</string>
              </property>
             </widget>
            </item>
            <item row="7" column="3">
             <widget class="QLineEdit" name="referRVal4"/>
            </item>
            <item row="5" column="2">
             <widget class="QLabel" name="label_13">
              <property name="text">
               <string>阻值（Ω）：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="3" column="5">
             <widget class="QComboBox" name="referCH2"/>
            </item>
            <item row="4" column="0">
             <widget class="QCheckBox" name="refer3">
              <property name="text">
               <string>参考3</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <spacer name="horizontalSpacer_3">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="3" column="3">
             <widget class="QLineEdit" name="referRVal2"/>
            </item>
            <item row="5" column="3">
             <widget class="QLineEdit" name="referRVal3"/>
            </item>
            <item row="5" column="0">
             <widget class="QLabel" name="label_14">
              <property name="text">
               <string>挡位：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="7" column="4">
             <widget class="QLabel" name="label_18">
              <property name="text">
               <string>通道：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="3">
             <widget class="QLineEdit" name="referRVal1">
              <property name="placeholderText">
               <string/>
              </property>
             </widget>
            </item>
            <item row="7" column="1">
             <widget class="QComboBox" name="referGear4"/>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="label_11">
              <property name="text">
               <string>挡位：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="5" column="5">
             <widget class="QComboBox" name="referCH3"/>
            </item>
            <item row="1" column="1">
             <widget class="QComboBox" name="referGear1"/>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_3">
              <property name="text">
               <string>挡位：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="5" column="4">
             <widget class="QLabel" name="label_15">
              <property name="text">
               <string>通道：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QCheckBox" name="refer2">
              <property name="text">
               <string>参考2</string>
              </property>
             </widget>
            </item>
            <item row="5" column="1">
             <widget class="QComboBox" name="referGear3"/>
            </item>
            <item row="1" column="4">
             <widget class="QLabel" name="label_9">
              <property name="text">
               <string>通道：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <spacer name="horizontalSpacer_5">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="3" column="4">
             <widget class="QLabel" name="label_10">
              <property name="text">
               <string>通道：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <spacer name="horizontalSpacer_4">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="6" column="1">
             <spacer name="horizontalSpacer_7">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="9" column="0">
             <widget class="QLabel" name="label_19">
              <property name="text">
               <string>挡位：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="9" column="1">
             <widget class="QComboBox" name="referGear5"/>
            </item>
            <item row="9" column="2">
             <widget class="QLabel" name="label_20">
              <property name="text">
               <string>阻值（Ω）：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="9" column="3">
             <widget class="QLineEdit" name="referRVal5"/>
            </item>
            <item row="9" column="4">
             <widget class="QLabel" name="label_21">
              <property name="text">
               <string>通道：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="9" column="5">
             <widget class="QComboBox" name="referCH5"/>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>744</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
