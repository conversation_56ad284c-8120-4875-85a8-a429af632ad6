#include "calibrationworker.h"

CalibrationWorker::CalibrationWorker(QObject *parent)
    : QObject(parent), m_abortRequested(false),
      m_calCommand<PERSON>and<PERSON>(nullptr), m_deviceCommandHandler(nullptr)
{
}

CalibrationWorker::~CalibrationWorker()
{
    m_abortRequested = true;
}

void CalibrationWorker::setDeviceConfig(const CalDeviceConfig &config)
{
    m_deviceConfig = config;

    // 使用QVector构造函数
    m_finalResults = QVector<QPair<double, double>>(config.num_channels, qMakePair(0.0, 0.0));
}

void CalibrationWorker::setCommandHandlers(CommandHandlerFunc calHandler, CommandHandlerFunc deviceHandler)
{
    m_calCommandHandler = calHandler;
    m_deviceCommandHandler = deviceHandler;
}

void CalibrationWorker::restartCalibration(int channel) // channel:1-n  cal_to_1220[i]:0-n-1
{
    // 检查命令处理函数是否已设置
    if (!m_calCommandHandler || !m_deviceCommandHandler)
    {
        emit logMessage("错误: 命令处理函数未设置");
        emit calibrationFinished(false);
        return;
    }

    emit logMessage(QString("开始 %1 通道 %2 参考阻值 %3 复校过程...").arg(m_deviceConfig.name).arg(channel).arg(m_deviceConfig.ref_values[0])); // ref_values[i]的所有值都相同
    m_abortRequested = false;

    // 关闭所有通道（初始化状态）
    // QByteArray closeSwitchs = createModbusCommand("01 10 00 03 00 02 04 00 00 00 00");
    // auto closeResult = sendCommand(closeSwitchs, "1220_ControlSwitch");
    // if (!closeResult.first) {
    //     emit logMessage("错误: 初始化关闭所有通道失败：" + closeResult.second);
    //     emit calibrationFinished(false);
    //     return;
    // }

    // QThread::sleep(2);

    const int max_attempts = 3;

    bool channel_passed = false; // 标记当前通道是否合格

    // 获取参考通道和标定通道
    int referChannel = m_deviceConfig.ref_index;              // 参考通道（1、2、3）
    int calChannel = m_deviceConfig.cal_to_1220[channel - 1]; // 当前标定通道（根据映射表）

    // 打开参考电阻所在通道和当前标定设备通道
    QByteArray openSwitchs = createModbusCommand(createModbusOpen1220Frame(referChannel, calChannel).toHex());
    auto openResult = sendCommand(openSwitchs, "1220_ControlSwitch");
    if (!openResult.first)
    {
        emit logMessage(QString("错误: 打开通道失败：%1").arg(openResult.second));
        emit calibrationFinished(false);
        return;
    }

    // QThread::sleep(30);  // 切换开关后等待30秒;

    emit logMessage(QString("等待转换开关设备稳定 (%1秒)...").arg(m_deviceConfig.switchDelay));

    // 使用可中断等待，提高中止响应性
    if (!interruptibleSleep(m_deviceConfig.switchDelay))
    {
        emit logMessage("标定过程被用户中止");
        emit calibrationFinished(false);
        return;
    }

    for (int attempt = 1; attempt <= max_attempts; ++attempt)
    {
        if (m_abortRequested)
        {
            emit logMessage("标定过程被用户中止");
            emit calibrationFinished(false);
            return;
        }

        auto [passed, result] = calibrate_unpassed_channels(channel - 1, attempt);
        if (passed)
        {
            channel_passed = true;
            break; // 标定成功，跳出循环
        }

        if (attempt == max_attempts && !channel_passed)
        {
            emit logMessage(QString("通道 %1 经过 %2 次标定仍不合格！").arg(channel).arg(max_attempts));
            emit calibrationFinished(false);
            return; // 立即停止整个标定流程 非break
        }
    }

    //    QThread::msleep(1000);

    //    // 标定完成后关闭所有通道
    //    closeResult = sendCommand(closeSwitchs, "1220_ControlSwitch");
    //    if (!closeResult.first) {
    //        emit logMessage(QString("错误: 关闭通道失败：%1").arg(closeResult.second));
    //        emit calibrationFinished(false);
    //        return;
    //    }

    emit calibrationFinished(true);
}

void CalibrationWorker::startCalibration()
{
    qDebug() << "CalibrationWorker Device Name:" << m_deviceConfig.name;
    // 检查命令处理函数是否已设置
    if (!m_calCommandHandler || !m_deviceCommandHandler)
    {
        emit logMessage("错误: 命令处理函数未设置");
        emit calibrationFinished(false);
        return;
    }

    emit calibrationStarted(); // 输出："标定进程已启动"

    emit logMessage(QString("开始 %1 标定过程...").arg(m_deviceConfig.name));
    m_abortRequested = false;

    // 关闭所有通道（初始化状态）
    QByteArray closeSwitchs = createModbusCommand("01 10 00 03 00 02 04 00 00 00 00");
    auto closeResult = sendCommand(closeSwitchs, "1220_ControlSwitch");
    if (!closeResult.first)
    {
        emit logMessage("错误: 初始化关闭所有通道失败：" + closeResult.second);
        emit calibrationFinished(false);
        return;
    }

    for (int i = 0; i < 20 && !QThread::currentThread()->isInterruptionRequested(); i++)
    {
        QThread::msleep(100); // 2s
    }

    // 存储每个通道的最后一轮阻值和偏差
    // QVector<QPair<double, double>> finalResults(m_deviceConfig.num_channels, {0.0, 0.0});
    const int max_attempts = 3;

    for (int ch = 0; ch < m_deviceConfig.num_channels; ++ch)
    {
        if (m_abortRequested)
        {
            emit logMessage("标定过程被用户中止");
            emit calibrationFinished(false);
            return;
        }

        emit logMessage(QString("开始标定通道 %1...").arg(ch + 1));
        emit calibrationProgress(ch, (ch * 100) / m_deviceConfig.num_channels);

        bool channel_passed = false; // 标记当前通道是否合格

        // 获取参考通道和标定通道
        int referChannel = m_deviceConfig.ref_index;     // 参考通道（1、2、3）
        int calChannel = m_deviceConfig.cal_to_1220[ch]; // 当前标定通道（根据映射表）从1开始

        // 打开参考电阻所在通道和当前标定设备通道
        QByteArray openSwitchs = createModbusCommand(createModbusOpen1220Frame(referChannel, calChannel).toHex());
        auto openResult = sendCommand(openSwitchs, "1220_ControlSwitch");
        if (!openResult.first)
        {
            emit logMessage(QString("错误: 打开通道失败：%1").arg(openResult.second));
            emit calibrationFinished(false);

            // 测试数据库用
            // emit saveResultsRequested(m_deviceConfig, finalResults);

            return;
        }

        emit logMessage(QString("等待转换开关设备稳定 (%1秒)...").arg(m_deviceConfig.switchDelay));

        // 使用可中断等待，提高中止响应性
        if (!interruptibleSleep(m_deviceConfig.switchDelay))
        {
            emit logMessage("标定过程被用户中止");
            emit calibrationFinished(false);
            return;
        }

        for (int attempt = 1; attempt <= max_attempts; ++attempt)
        {
            if (m_abortRequested)
            {
                emit logMessage("标定过程被用户中止");
                emit calibrationFinished(false);
                return;
            }

            auto [passed, result] = calibrate_unpassed_channels(ch, attempt);
            if (passed)
            {
                channel_passed = true;
                m_finalResults[ch] = result;
                // emit logMessage(QString("通道 %1 标定成功!").arg(ch+1));
                break; // 标定成功，跳出循环
            }

            if (attempt == max_attempts && !channel_passed)
            {
                emit logMessage(QString("通道 %1 经过 %2 次标定仍不合格！").arg(ch + 1).arg(max_attempts));
                m_finalResults[ch] = result;

                // emit calibrationFinished(false);
                // return; // 立即停止整个标定流程 非break
            }
        }

        QThread::msleep(1000);

        //        // 标定完成后关闭所有通道
        //        closeResult = sendCommand(closeSwitchs, "1220_ControlSwitch");
        //        if (!closeResult.first) {
        //            emit logMessage(QString("错误: 关闭通道失败：%1").arg(closeResult.second));
        //            emit calibrationFinished(false);
        //            return;
        //        }
    }

    emit logMessage("所有通道标定完成!");
    emit calibrationProgress(m_deviceConfig.num_channels, 100);
    emit calibrationFinished(true);

    // 保存标定结果到数据库中 失败也会校完整个流程 取消自动保存
    // emit saveResultsRequested(m_deviceConfig, m_finalResults);
}

QVector<QPair<double, double>> CalibrationWorker::getCalibrationResults() const
{
    return m_finalResults;
}

void CalibrationWorker::abortCalibration()
{
    m_abortRequested = true;
    emit logMessage("正在中止标定过程...");

    // 请求当前线程中断，提高响应性
    if (QThread::currentThread())
    {
        QThread::currentThread()->requestInterruption();
    }
}

double CalibrationWorker::getThresholdValue() const
{
    // 标准设备阈值映射
    static const QMap<QString, double> standardThresholdMap = {
        // 原有设备
        {"619A RTD PLUS", 0.01},
        {"618A RTD PLUS", 0.02},
        {"618A RTD", 0.03},
        {"618A", 4.0},
        {"618A PLUS", 2.0},
        {"618A PLUS(6NTC+2P-AP23)", 2.0},
        {"619A PLUS", 0.4},
        // 新增设备
        {"TM14RD-PT100", 0.4},
        {"TM14RD-PT1000", 0.4},
        {"TM14ND", 0.4},
        {"TM14ND-T", 0.1},
        {"TM14ND-P", 0.1},
        {"TM14ND-P-S", 0.1},
        {"TM24ND-P-S", 0.4},

        {"TM18ND-P", 0.1},
        {"TM18RD-P", 0.05},

        {"H-LCW-22B", 0.1},
        {"TM222ND-P", 0.1},
        {"TM224ND-P", 0.1},
        {"TM228ND-P", 0.1},
        {"618A NTC-32", 4.0},
        // "618A NTC-32-TIME" 在下面单独处理
        {"619A NTC-32 PLUS", 0.4},
        {"619A RTD-32 PLUS", 0.01},

        {"1611A-HT(PT100)", 0.02},
        {"1611A-HT(PT1000)", 0.2},
    };

    // 1618A 系列阈值映射
    static const QMap<QString, QMap<QString, double>> thresholdMap1618A = {
        {"NTC", {{"01", 0.4}, {"02", 2}, {"03", 4}, {"default", 0.0}}},
        {"RTD", {{"01", 0.003}, {"02", 0.01}, {"03", 0.0}, {"default", 0.0}}},
        {"TC", {{"01", 0.0}, {"02", 0.0}, {"03", 0.0}, {"default", 0.0}}}};

    const QString prefix = "1618A-";
    if (m_deviceConfig.name.startsWith(prefix))
    {
        QString suffix = m_deviceConfig.name.mid(prefix.length()); // 如 "N-NTC-01" 或 "L-NTC-01"
        QStringList parts = suffix.split('-');
        if (parts.size() == 3) // 新格式：1618A-N-NTC-01 或 1618A-L-NTC-01
        {
            const QString &type = parts[1]; // NTC/RTD/TC
            const QString &code = parts[2]; // 01/02/03

            auto typeIt = thresholdMap1618A.find(type);
            if (typeIt != thresholdMap1618A.end())
            {
                const auto &codeMap = typeIt.value();
                // 优先查找具体 code 阈值
                if (codeMap.contains(code))
                {
                    return codeMap.value(code);
                }
                // code 不存在时使用默认阈值
                else if (codeMap.contains("default"))
                {
                    return codeMap.value("default");
                }
            }
        }
        else if (parts.size() == 2) // 兼容旧格式：1618A-NTC-01
        {
            const QString &type = parts[0];
            const QString &code = parts[1];

            auto typeIt = thresholdMap1618A.find(type);
            if (typeIt != thresholdMap1618A.end())
            {
                const auto &codeMap = typeIt.value();
                // 优先查找具体 code 阈值
                if (codeMap.contains(code))
                {
                    return codeMap.value(code);
                }
                // code 不存在时使用默认阈值
                else if (codeMap.contains("default"))
                {
                    return codeMap.value("default");
                }
            }
        }
        // 格式不符合或未匹配，返回默认阈值
        return 0.4;
    }

    // 特殊处理 "618A NTC-32-TIME" 基于电阻范围
    if (m_deviceConfig.name == "618A NTC-32-TIME")
    {
        // 获取参考电阻值
        double referRValue = m_deviceConfig.ref_values[0]; // ref_values中所有值相同

        // 定义电阻范围和对应的阈值
        struct RangeThreshold
        {
            double minR;      // 最小电阻值
            double maxR;      // 最大电阻值
            double threshold; // 对应阈值
        };

        // 定义不同挡位的电阻范围和对应阈值
        const RangeThreshold ranges[] = {
            {900, 1100, 0.1},   // 1k挡位
            {4500, 5500, 0.2},  // 5k挡位
            {9500, 10500, 0.3}, // 10k挡位
            {19000, 21000, 0.5} // 20k挡位
        };

        // 遍历查找匹配的范围
        bool matched = false;
        for (const auto &range : ranges)
        {
            if (referRValue >= range.minR && referRValue <= range.maxR)
            {
                matched = true;
                return range.threshold;
            }
        }

        // 如果没有匹配任何范围，使用默认阈值
        if (!matched)
        {
            qWarning() << "警告：618A NTC-32-TIME 的电阻值" << referRValue
                       << "不在任何标准范围内，使用默认阈值0.1";
            return 0.1; // 使用最严格的阈值作为默认值
        }
    }

    // 对于其他设备，使用标准映射或默认值0.4
    return standardThresholdMap.value(m_deviceConfig.name, 0.4);
}

bool CalibrationWorker::is_channel_passed(const QVector<double> &deviations)
{
    // 获取基于设备型号和电阻范围（如适用）的阈值
    double threshold = getThresholdValue();

    qDebug() << "当前阈值：" << threshold;

    if (deviations.size() < 3)
        return false;

    // 检查最后三次测量的偏差是否都在阈值内
    for (int i = deviations.size() - 3; i < deviations.size(); ++i)
    {
        if (deviations[i] >= threshold)
            return false; // 测试偏差
    }

    return true;
}

QVector<double> CalibrationWorker::read_resistances(int channel)
{
    // 获取当前设备的精度类型
    FloatPrecision precision = getDevicePrecision();

    QByteArray frame = createModbusReadFrame(0x01, m_deviceConfig.read_addr, channel, 1, precision);

    QPair<bool, QString> result = sendCommand(
        createModbusCommand(QString::fromLatin1(frame.toHex())),
        "Cal_ReadARoundRVals");

    QVector<double> resistances;
    if (result.first)
    {
        double resistance = result.second.toDouble();
        resistances.append(resistance);
    }
    else
    {
        emit logMessage(QString("读取通道 %1 数据失败: %2").arg(channel + 1).arg(result.second));
        resistances.append(0.0); // 错误时返回默认值
    }

    return resistances; // 读取单通道值只用到了resistances[0]
}

QPair<bool, QPair<double, double>> CalibrationWorker::calibrate_unpassed_channels(int channel, int attempt)
{
    emit logMessage(QString("通道 %1 第 %2 次标定开始...").arg(channel + 1).arg(attempt));

    // 获取当前设备的精度类型
    FloatPrecision precision = getDevicePrecision();

    // 写入参考阻值
    QByteArray frame = createModbusWriteFrame(0x01, m_deviceConfig.write_addr, channel, m_deviceConfig.ref_values[channel], precision);
    // 为TM24ND-P-S使用专门的命令标识符
    QString commandId = (m_deviceConfig.name == "TM24ND-P-S") ? "Cal_WriteCalParams_TM24ND" : "Cal_WriteCalParams";
    auto writeResult = sendCommand(createModbusCommand(QString::fromLatin1(frame.toHex())), commandId);
    if (!writeResult.first)
    {
        emit logMessage(QString("通道 %1 写入参考阻值失败：%2").arg(channel + 1).arg(writeResult.second));
        return {false, {0.0, 0.0}};
    }

    // 等待设备稳定
    emit logMessage(QString("已写入外部测量准确值，等待设备稳定 (5秒)..."));
    for (int i = 0; i < 5; ++i)
    {
        if (m_abortRequested)
            return {false, {0.0, 0.0}};
        QThread::sleep(1);
    }

    // 获取当前设备和电阻范围的阈值
    double threshold = getThresholdValue();

    // 读取10轮阻值数据
    QVector<double> deviations;
    double lastResistance = 0.0;
    for (int round = 0; round < 10; ++round)
    {
        if (m_abortRequested)
            return {false, {0.0, 0.0}};

        QVector<double> resistances = read_resistances(channel); // channel = 0 -> CH1
        if (resistances[0] == 0.0)
        {
            emit logMessage(QString("通道 %1 读取阻值失败，轮次：%2").arg(channel + 1).arg(round + 1));
            return {false, {0.0, 0.0}};
        }

        lastResistance = resistances[0];
        double dev = std::abs(lastResistance - m_deviceConfig.ref_values[channel]);
        deviations.append(dev);

        // 发送信号更新UI
        bool roundPassed = (dev < threshold);

        emit updateChannelData(channel, lastResistance, dev, roundPassed);

        emit logMessage(QString("通道 %1 轮次 %2: 阻值=%3, 偏差=%4, %5")
                            .arg(channel + 1)
                            .arg(round + 1)
                            .arg(lastResistance, 0, 'f', 6)
                            .arg(dev, 0, 'f', 6)
                            .arg(roundPassed ? "合格" : "不合格"));

        // 优化：将1秒等待改为可中断的等待
        for (int i = 0; i < 10; ++i)
        {
            if (m_abortRequested)
                return {false, {0.0, 0.0}};
            QThread::msleep(100);
        }
    }

    // 判断是否合格
    bool passed = is_channel_passed(deviations);
    if (passed)
    {
        emit logMessage(QString("通道 %1 标定成功！").arg(channel + 1));
    }
    else
    {
        emit logMessage(QString("通道 %1 第 %2 次标定失败。").arg(channel + 1).arg(attempt));
    }

    double lastDeviation = deviations.last();
    return {passed, {lastResistance, lastDeviation}};
}

// 添加一个新的辅助方法，用于确定设备需要的浮点精度类型
FloatPrecision CalibrationWorker::getDevicePrecision() const
{
    // 使用单精度浮点数的设备列表
    static const QSet<QString> singlePrecisionDevices = {
        "618A NTC-32-TIME",
        "1611A-HT(PT100)",
        "1611A-HT(PT1000)",
    };

    // 如果设备在单精度列表中，返回单精度，否则返回双精度
    return singlePrecisionDevices.contains(m_deviceConfig.name) ? FloatPrecision::Single : FloatPrecision::Double;
}

bool CalibrationWorker::interruptibleSleep(int seconds)
{
    for (int i = 0; i < seconds; ++i)
    {
        if (m_abortRequested || QThread::currentThread()->isInterruptionRequested())
        {
            return false; // 被中断
        }
        QThread::sleep(1);
    }
    return true; // 正常完成
}

QByteArray CalibrationWorker::createModbusReadFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, int numChannels, FloatPrecision precision)
{
    // 计算每个通道占用的寄存器数量
    int registersPerChannel = (precision == FloatPrecision::Single) ? 2 : 4;

    // 计算起始寄存器地址
    uint16_t addr = firstAddr + channel * registersPerChannel;
    uint8_t addr_high = (addr >> 8) & 0xFF; // 提取高字节
    uint8_t addr_low = addr & 0xFF;         // 提取低字节

    // 计算需要读取的寄存器数量
    uint16_t numRegisters = numChannels * registersPerChannel;
    uint8_t numRegisters_high = (numRegisters >> 8) & 0xFF; // 提取高字节
    uint8_t numRegisters_low = numRegisters & 0xFF;         // 提取低字节

    // 构造 Modbus RTU 请求帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(deviceAddr));        // 设备地址
    frame.append(static_cast<char>(0x03));              // 功能码 03（读取保持寄存器）
    frame.append(static_cast<char>(addr_high));         // 寄存器地址高字节
    frame.append(static_cast<char>(addr_low));          // 寄存器地址低字节
    frame.append(static_cast<char>(numRegisters_high)); // 寄存器数量高字节
    frame.append(static_cast<char>(numRegisters_low));  // 寄存器数量低字节

    return frame;
}

QByteArray CalibrationWorker::createModbusWriteFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, double ref_value, FloatPrecision precision)
{
    // 计算每个通道占用的寄存器数量
    int registersPerChannel = (precision == FloatPrecision::Single) ? 2 : 4;

    // 计算该通道的寄存器地址
    uint16_t addr = firstAddr + channel * registersPerChannel;
    uint8_t addr_high = (addr >> 8) & 0xFF; // 高字节
    uint8_t addr_low = addr & 0xFF;         // 低字节

    // 准备数据 - 多个寄存器写入统一使用小端模式
    QByteArray byteArray;
    QDataStream stream(&byteArray, QIODevice::WriteOnly);
    stream.setByteOrder(QDataStream::LittleEndian);

    if (precision == FloatPrecision::Single)
    {
        // 对于单精度，将 double 转换为 float，并确保只写入 4 个字节
        float float_value = static_cast<float>(ref_value);
        byteArray.resize(4);                       // 4 个字节
        memcpy(byteArray.data(), &float_value, 4); // 写入数据
    }
    else
    {
        // 对于双精度，直接写入 double，确保写入 8 个字节
        byteArray.resize(8);                     // 8 个字节
        memcpy(byteArray.data(), &ref_value, 8); // 写入数据
    }

    // 计算字节数
    int byteCount = (precision == FloatPrecision::Single) ? 4 : 8;

    // 构造 Modbus-RTU 帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(deviceAddr));          // 设备地址
    frame.append(static_cast<char>(0x10));                // 功能码 10（写多个寄存器）
    frame.append(static_cast<char>(addr_high));           // 寄存器地址高字节
    frame.append(static_cast<char>(addr_low));            // 寄存器地址低字节
    frame.append(static_cast<char>(0x00));                // 寄存器数量高字节
    frame.append(static_cast<char>(registersPerChannel)); // 寄存器数量低字节
    frame.append(static_cast<char>(byteCount));           // 字节数
    frame.append(byteArray);                              // 数据

    return frame;
}

QByteArray CalibrationWorker::createModbusOpen1220Frame(int referChannel, int calChannel)
{
    // 创建一个4字节的数组，初始化为0
    QByteArray byteArray(4, 0);

    // 设置referChannel对应的位
    if (referChannel >= 1 && referChannel <= 20)
    {
        int byteIndex = (referChannel - 1) / 8;   // 确定在哪个字节
        int bitPosition = (referChannel - 1) % 8; // 确定在字节中的位置
        char byte = byteArray[byteIndex];         // 获取当前字节
        byte |= (0x01 << bitPosition);            // 设置位
        byteArray[byteIndex] = byte;              // 将修改后的字节写回数组
    }

    // 设置calChannel对应的位
    if (calChannel >= 1 && calChannel <= 20)
    {
        int byteIndex = (calChannel - 1) / 8;
        int bitPosition = (calChannel - 1) % 8;
        char byte = byteArray[byteIndex];
        byte |= (0x01 << bitPosition);
        byteArray[byteIndex] = byte;
    }

    // 构造 Modbus-RTU 帧（不含 CRC）
    QByteArray frame;
    frame.append(static_cast<char>(0x01)); // 设备地址
    frame.append(static_cast<char>(0x10)); // 功能码 10
    frame.append(static_cast<char>(0x00)); // 寄存器地址高字节
    frame.append(static_cast<char>(0x03)); // 寄存器地址低字节
    frame.append(static_cast<char>(0x00)); // 寄存器数量高字节
    frame.append(static_cast<char>(0x02)); // 寄存器数量低字节
    frame.append(static_cast<char>(0x04)); // 字节数
    frame.append(byteArray);               // 添加生成的4字节数据

    return frame;
}

QByteArray CalibrationWorker::createModbusCommand(const QString &hexCommand)
{
    // 移除所有空格
    QString cleanCommand = hexCommand.simplified().remove(' ');

    // 将十六进制字符串转换为 QByteArray
    QByteArray cmd = QByteArray::fromHex(cleanCommand.toLatin1());

    // 计算并添加 CRC
    uint16_t crc = calculateCRC16(cmd);
    cmd.append(static_cast<char>(crc & 0xFF));
    cmd.append(static_cast<char>((crc >> 8) & 0xFF));

    return cmd;
}

uint16_t CalibrationWorker::calculateCRC16(const QByteArray &data)
{
    uint16_t crc = 0xFFFF;
    for (char ch : data)
    {
        crc ^= static_cast<uint8_t>(ch);
        for (int i = 0; i < 8; ++i)
        {
            if (crc & 0x0001)
            {
                crc = (crc >> 1) ^ 0xA001;
            }
            else
            {
                crc = crc >> 1;
            }
        }
    }
    return crc;
}

QPair<bool, QString> CalibrationWorker::sendCommand(const QByteArray &command, const QString &commandType)
{
    // 根据命令类型选择合适的命令处理函数
    if (commandType.startsWith("Cal_"))
    {
        if (m_calCommandHandler)
        {
            return m_calCommandHandler(command, commandType);
        }
    }
    else if (commandType.startsWith("1220_"))
    {
        if (m_deviceCommandHandler)
        {
            return m_deviceCommandHandler(command, commandType);
        }
    }

    return {false, "未知的命令类型或命令处理函数未设置"};
}

// QByteArray CalibrationWorker::createModbusReadFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, int numChannels)
// {
//     // 计算起始寄存器地址
//     uint16_t addr = firstAddr + channel * 4;
//     uint8_t addr_high = (addr >> 8) & 0xFF;  // 提取高字节
//     uint8_t addr_low = addr & 0xFF;          // 提取低字节

//     // 计算需要读取的寄存器数量
//     uint16_t numRegisters = numChannels * 4;
//     uint8_t numRegisters_high = (numRegisters >> 8) & 0xFF;  // 提取高字节
//     uint8_t numRegisters_low = numRegisters & 0xFF;          // 提取低字节

//     // 构造 Modbus RTU 请求帧（不含 CRC）
//     QByteArray frame;
//     frame.append(static_cast<char>(deviceAddr));        // 设备地址
//     frame.append(static_cast<char>(0x03));              // 功能码 03（读取保持寄存器）
//     frame.append(static_cast<char>(addr_high));         // 寄存器地址高字节
//     frame.append(static_cast<char>(addr_low));          // 寄存器地址低字节
//     frame.append(static_cast<char>(numRegisters_high)); // 寄存器数量高字节
//     frame.append(static_cast<char>(numRegisters_low));  // 寄存器数量低字节

//     return frame;
// }

// QByteArray CalibrationWorker::createModbusWriteFrame(uint8_t deviceAddr, uint16_t firstAddr, int channel, double ref_value)
// {
//     // 计算该通道的寄存器地址（每个通道占用 4 个寄存器）
//     uint16_t addr = firstAddr + channel * 4;
//     uint8_t addr_high = (addr >> 8) & 0xFF; // 高字节
//     uint8_t addr_low = addr & 0xFF;         // 低字节

//     // 将 double 类型转换为小端字节序的 8 字节数据
//     QByteArray byteArray;
//     QDataStream stream(&byteArray, QIODevice::WriteOnly);
//     stream.setByteOrder(QDataStream::LittleEndian);
//     stream << ref_value;

//     // 构造 Modbus-RTU 帧（不含 CRC）
//     QByteArray frame;
//     frame.append(static_cast<char>(deviceAddr));        // 设备地址（示例为 01，可配置）
//     frame.append(static_cast<char>(0x10));        // 功能码 10（写多个寄存器）
//     frame.append(static_cast<char>(addr_high));   // 寄存器地址高字节
//     frame.append(static_cast<char>(addr_low));    // 寄存器地址低字节
//     frame.append(static_cast<char>(0x00));        // 寄存器数量高字节
//     frame.append(static_cast<char>(0x04));        // 寄存器数量低字节（4 个寄存器）
//     frame.append(static_cast<char>(0x08));        // 字节数（8 字节）
//     frame.append(byteArray);                      // 数据（8 字节）

//     return frame;
// }
