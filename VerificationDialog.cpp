﻿#include "VerificationDialog.h"
#include "ui_VerificationDialog.h"
#include <QSettings>
#include <QMessageBox>
#include <QScreen>

VerificationDialog::VerificationDialog(QWidget *parent) : QDialog(parent),
                                                          ui(new Ui::VerificationDialog)
{
    ui->setupUi(this);

    // // 优化布局，使用QWidget作为容器
    // QVBoxLayout* mainLayout = qobject_cast<QVBoxLayout*>(this->layout());
    // if (mainLayout) {
    //     mainLayout->setSizeConstraint(QLayout::SetDefaultConstraint);
    //     mainLayout->setContentsMargins(4, 4, 4, 4); // 减少边距
    //     mainLayout->setSpacing(4); // 减少间距
    // }

    // // 添加这些行以启用布局缓存
    // QWidget* centralWidget = this;
    // centralWidget->setAttribute(Qt::WA_StaticContents);
    // centralWidget->setAttribute(Qt::WA_OpaquePaintEvent);
    // centralWidget->setAttribute(Qt::WA_NoSystemBackground);

    // 设置最小和最大尺寸
    // this->setMinimumSize(300, 200);
    // this->setMaximumSize(800, 600);

    // 调整窗口大小以适应内容
    // this->adjustSize();

    // Calculate 80% of screen size and enforce it
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int width = screenGeometry.width() * 0.7;
    int height = screenGeometry.height() * 0.85;

    // Set minimum and fixed sizes to ensure the dialog is actually 80% of screen size
    setMinimumSize(width, height);
    resize(width, height);

    // 设置窗口标题
    setWindowTitle("校准");

    // 移除帮助按钮并添加系统菜单和最大化按钮
    setWindowFlags((windowFlags() & ~Qt::WindowContextHelpButtonHint) |
                   Qt::WindowSystemMenuHint | Qt::WindowMaximizeButtonHint);

    QStringList boardCardModels = {"1618A-N18P-0B05", "1618A-N18P-0B06", "1618A-N19P-0B05",
                                   "1618A-N19P-0B06", "1618A-N18P-1B05", "1618A-N18P-1B06",
                                   "1618A-N19P-1B05", "1618A-N19P-1B06", "1618A-N18T",
                                   "1618A-N19T", "1618A-N18D", "1618A-N19D",
                                   "1618A-R18P-0B05", "1618A-R18P-0B06", "1618A-R19P-0B05",
                                   "1618A-R19P-0B06", "1618A-R18P-1B05", "1618A-R18P-1B06",
                                   "1618A-R19P-1B05", "1618A-R19P-1B06", "1618A-R18T",
                                   "1618A-R19T", "1618A-R18D", "1618A-R19D"};

    // 生成RTD型号列表
    QStringList rtdModels;
    // for (const QString &model : boardCardModels) {
    //     QString rtdModel = model;       // 复制一份可修改的字符串
    //     rtdModel.replace("-N", "-R");  // 替换-N为-R
    //     rtdModels.append(rtdModel);
    // }
    QStringList allModels = boardCardModels + rtdModels;
    ui->boardCardModel_Combo_Adj->addItems(allModels);

    ui->tempBridgeModel->addItem("712A");
    ui->refResistorModel->addItem("712A");
    ui->voltSourceModel->addItem("712A");
    ui->refResistorModel4->addItem("712A");
    ui->refResistorModel5->addItem("712A");

    ui->abortCal_Adj->setEnabled(false);

    loadSettings();

    // 手动连接按钮的信号到槽函数
    connect(ui->buttonBox, &QDialogButtonBox::accepted, this, &VerificationDialog::accept);
    connect(ui->buttonBox, &QDialogButtonBox::rejected, this, &VerificationDialog::reject);

    // 初始化重绘延迟定时器
    resizeTimer = new QTimer(this);
    resizeTimer->setSingleShot(true);
    connect(resizeTimer, &QTimer::timeout, this, [this]()
            { this->update(); });

    QList<QComboBox *> comboBoxes = this->findChildren<QComboBox *>();
    for (QComboBox *box : comboBoxes)
    {
        box->setMaxVisibleItems(6);
    }
}

// 添加resizeEvent函数
void VerificationDialog::resizeEvent(QResizeEvent *event)
{
    // 取消之前的计时器
    if (resizeTimer->isActive())
        resizeTimer->stop();

    // 200毫秒后重绘
    resizeTimer->start(200);

    QDialog::resizeEvent(event);
}

void VerificationDialog::showEvent(QShowEvent *event)
{
    // 先调用基类的 showEvent，确保正常显示流程
    QDialog::showEvent(event);

    // 将对话框居中于屏幕可用区域
    QScreen *screen = QApplication::primaryScreen();
    QRect availableRect = screen->availableGeometry(); // 获取屏幕可用区域
    QPoint center = availableRect.center();            // 获取屏幕中心的坐标
    int x = center.x() - this->width() / 2;            // 计算对话框左上角的 x 坐标
    int y = center.y() - this->height() / 2;           // 计算对话框左上角的 y 坐标
    this->move(x, y);                                  // 移动对话框到计算出的位置
}

VerificationDialog::~VerificationDialog()
{
    delete ui;
}

void VerificationDialog::on_readBtn_AdjDialog_clicked()
{
    emit signal_readCommand_AdjDialog(ui->deviceModel_Adj->text(), "readNum");
}

void VerificationDialog::on_readBtn_Board_AdjDialog_clicked()
{
    emit signal_readCommand_AdjDialog(ui->deviceModel_Adj->text(), "readBoardCardNumber");
}

void VerificationDialog::on_readBtn_Board_AdjDialog_2_clicked()
{
    emit signal_readCommand_AdjDialog(ui->deviceModel_Adj->text(), "readBoardCardModel");
}

void VerificationDialog::on_startCal_Adj_clicked()
{
    if (ui->deviceModel_Adj->text() == "1618A-N" || ui->deviceModel_Adj->text() == "1618A-L")
    {
        if (ui->serialNumEdit_Board_Adj->text().isEmpty() || ui->reportNum->text().isEmpty())
        {
            QMessageBox::warning(this, "警告", "请先执行系统参数校准。");
            return;
        }
    }
    else
    {
        if (ui->serialNumEdit_Adj->text().isEmpty() || ui->reportNum->text().isEmpty())
        {
            QMessageBox::warning(this, "警告", "请先执行系统参数校准。");
            return;
        }
    }

    if (ui->deviceModel_Adj->text() == "1618A-N" || ui->deviceModel_Adj->text() == "1618A-L")
    {
        QString cardType_Accuracy = ui->serialNumEdit_Board_Adj_2->text(); // 板卡类型+精度

        // 开始标定 需要传回：设备型号 BK-NTC2501 - NTC-01  、通道个数
        // 格式：1618A-N-NTC-01 或 1618A-L-NTC-01
        emit signal_startCal_AdjDialog(ui->deviceModel_Adj->text() + "-" + cardType_Accuracy, "startCal");
    }
    else
    {
        // 开始标定前 限制其执行读取设备序列号等操作 便于后续生成项目历史记录用

        // 开始标定 需要传回：设备型号、通道个数
        emit signal_startCal_AdjDialog(ui->deviceModel_Adj->text(), "startCal");
    }
}

void VerificationDialog::on_signal_readCommand_AdjDialog_Result(const QString &readResultData, const QString &featureCode)
{
    if (featureCode == "readNum")
    {
        ui->serialNumEdit_Adj->clear();
        ui->serialNumEdit_Adj->setText(readResultData);

        // 根据读取到的序列号 生成一个报告编号并填入
        QDateTime currentDateTime = QDateTime::currentDateTime();
        QString currentDateTimeString = currentDateTime.toString("yyyyMMddHH");

        ui->reportNum->clear();
        ui->reportNum->setText(readResultData + '-' + currentDateTimeString);
    }
    else if (featureCode == "readBoardCardNumber")
    {
        ui->serialNumEdit_Board_Adj->clear();
        ui->serialNumEdit_Board_Adj->setText(readResultData);

        // 根据读取到的序列号 生成一个报告编号并填入 1618A报告编号中的设备序列号为板卡编号
        QDateTime currentDateTime = QDateTime::currentDateTime();
        QString currentDateTimeString = currentDateTime.toString("yyyyMMddHH");

        ui->reportNum->clear();
        ui->reportNum->setText(readResultData + '-' + currentDateTimeString);
    }
    else if (featureCode == "readBoardCardModel")
    {
        ui->serialNumEdit_Board_Adj_2->clear();
        ui->serialNumEdit_Board_Adj_2->setText(readResultData);
    }
}

void VerificationDialog::initVerificationDialog(const QString &deviceModel)
{
    ui->deviceModel_Adj->clear();
    ui->deviceModel_Adj->setText(deviceModel);

    // 首先重置所有组件到默认状态
    ui->readBtn_AdjDialog->setEnabled(true);
    ui->line_2->setVisible(false);
    ui->label_2->setVisible(false);
    ui->label_3->setVisible(false);
    ui->label->setVisible(false);
    ui->serialNumEdit_Board_Adj->setVisible(false);
    ui->readBtn_Board_AdjDialog->setVisible(false);
    ui->serialNumEdit_Board_Adj_2->setVisible(false);
    ui->readBtn_Board_AdjDialog_2->setVisible(false);
    ui->boardCardModel_Combo_Adj->setVisible(false);

    // 透传模式组件默认隐藏
    ui->transparentModeLabel_Adj->setVisible(false);
    ui->enterTransparentMode_Adj->setVisible(false);
    ui->quitTransparentMode_Adj->setVisible(false);

    // 检查是否为透传模式设备
    QStringList transparentModeDevices = {"1611A-HT(PT100)", "1611A-HT(PT1000)", "1618A-L"};
    if (transparentModeDevices.contains(deviceModel))
    {
        // 透传模式设备：显示透传模式组件
        ui->transparentModeLabel_Adj->setVisible(true);
        ui->enterTransparentMode_Adj->setVisible(true);
        ui->quitTransparentMode_Adj->setVisible(true);
    }

    // 根据设备类型进行特殊设置
    if (deviceModel == "1618A-N" || deviceModel == "1618A-L")
    {
        // 1618A-N和1618A-L设备：禁用普通读取按钮，显示板卡相关组件
        ui->readBtn_AdjDialog->setEnabled(false);
        ui->line_2->setVisible(true);
        ui->label_2->setVisible(true);
        ui->label_3->setVisible(true);
        ui->label->setVisible(true);
        ui->serialNumEdit_Board_Adj->setVisible(true);
        ui->readBtn_Board_AdjDialog->setVisible(true);
        ui->serialNumEdit_Board_Adj_2->setVisible(true);
        ui->readBtn_Board_AdjDialog_2->setVisible(true);
        ui->boardCardModel_Combo_Adj->setVisible(true);
    }
    else
    {
        // 其他设备：使用普通读取按钮，隐藏板卡相关组件（已在重置中设置）
        ui->readBtn_AdjDialog->setEnabled(true);
    }
}

// 记忆功能
void VerificationDialog::loadSettings()
{
    QSettings settings("zhice-elec", "Auto_TCal");
    if (!settings.contains("calibrator"))
        return;

    ui->refResistorDateTimeEdit->setDateTime(QDateTime::fromString(settings.value("refResistorDateTimeEdit", "").toString(), Qt::ISODate));
    ui->tempBridgeDateTimeEdit->setDateTime(QDateTime::fromString(settings.value("tempBridgeDateTimeEdit", "").toString(), Qt::ISODate));
    ui->voltSourceDateTimeEdit->setDateTime(QDateTime::fromString(settings.value("voltSourceDateTimeEdit", "").toString(), Qt::ISODate));
    ui->refResistorDateTimeEdit4->setDateTime(QDateTime::fromString(settings.value("refResistorDateTimeEdit4", "").toString(), Qt::ISODate));
    ui->refResistorDateTimeEdit5->setDateTime(QDateTime::fromString(settings.value("refResistorDateTimeEdit5", "").toString(), Qt::ISODate));

    ui->voltSourceSerialNum->setText(settings.value("voltSourceSerialNum", "").toString());
    ui->refResistorSerialNum->setText(settings.value("refResistorSerialNum", "").toString());
    ui->tempBridgeSerialNum->setText(settings.value("tempBridgeSerialNum", "").toString());
    ui->refResistorSerialNum4->setText(settings.value("refResistorSerialNum4", "").toString());
    ui->refResistorSerialNum5->setText(settings.value("refResistorSerialNum5", "").toString());

    ui->voltSourceModel->setCurrentIndex(settings.value("voltSourceModel", 0).toInt());
    ui->refResistorModel->setCurrentIndex(settings.value("refResistorModel", 0).toInt());
    ui->tempBridgeModel->setCurrentIndex(settings.value("tempBridgeModel", 0).toInt());
    ui->refResistorModel4->setCurrentIndex(settings.value("refResistorModel4", 0).toInt());
    ui->refResistorModel5->setCurrentIndex(settings.value("refResistorModel5", 0).toInt());

    ui->tempBridgeCheck->setChecked(settings.value("tempBridgeCheck", false).toBool());
    ui->refResistorCheck->setChecked(settings.value("refResistorCheck", false).toBool());
    ui->voltSourceCheck->setChecked(settings.value("voltSourceCheck", false).toBool());
    ui->refResistorCheck4->setChecked(settings.value("refResistorCheck4", false).toBool());
    ui->refResistorCheck5->setChecked(settings.value("refResistorCheck5", false).toBool());

    ui->ambientTemp->setText(settings.value("ambientTemp", "").toString());
    ui->ambientHum->setText(settings.value("ambientHum", "").toString());
    ui->ambientPre->setText(settings.value("ambientPre", "").toString());
    ui->calibrator->setText(settings.value("calibrator", "").toString());
    // ui->reportNum->setText(settings.value("reportNum", "").toString());
}

void VerificationDialog::closeEvent(QCloseEvent *event)
{
    // 保存设置
    QSettings settings("zhice-elec", "Auto_TCal");
    settings.setValue("voltSourceDateTimeEdit", ui->voltSourceDateTimeEdit->dateTime().toString(Qt::ISODate));
    settings.setValue("refResistorDateTimeEdit", ui->refResistorDateTimeEdit->dateTime().toString(Qt::ISODate));
    settings.setValue("tempBridgeDateTimeEdit", ui->tempBridgeDateTimeEdit->dateTime().toString(Qt::ISODate));
    settings.setValue("refResistorDateTimeEdit4", ui->refResistorDateTimeEdit4->dateTime().toString(Qt::ISODate));
    settings.setValue("refResistorDateTimeEdit5", ui->refResistorDateTimeEdit5->dateTime().toString(Qt::ISODate));
    settings.setValue("voltSourceSerialNum", ui->voltSourceSerialNum->text());
    settings.setValue("refResistorSerialNum", ui->refResistorSerialNum->text());
    settings.setValue("tempBridgeSerialNum", ui->tempBridgeSerialNum->text());
    settings.setValue("refResistorSerialNum4", ui->refResistorSerialNum4->text());
    settings.setValue("refResistorSerialNum5", ui->refResistorSerialNum5->text());
    settings.setValue("voltSourceModel", ui->voltSourceModel->currentIndex());
    settings.setValue("refResistorModel", ui->refResistorModel->currentIndex());
    settings.setValue("tempBridgeModel", ui->tempBridgeModel->currentIndex());
    settings.setValue("refResistorModel4", ui->refResistorModel4->currentIndex());
    settings.setValue("refResistorModel5", ui->refResistorModel5->currentIndex());
    settings.setValue("tempBridgeCheck", ui->tempBridgeCheck->isChecked());
    settings.setValue("refResistorCheck", ui->refResistorCheck->isChecked());
    settings.setValue("voltSourceCheck", ui->voltSourceCheck->isChecked());
    settings.setValue("refResistorCheck4", ui->refResistorCheck4->isChecked());
    settings.setValue("refResistorCheck5", ui->refResistorCheck5->isChecked());

    settings.setValue("ambientTemp", ui->ambientTemp->text());
    settings.setValue("ambientHum", ui->ambientHum->text());
    settings.setValue("ambientPre", ui->ambientPre->text());
    settings.setValue("calibrator", ui->calibrator->text());
    // settings.setValue("reportNum", ui->reportNum->text());

    // 调用基类的closeEvent方法
    QDialog::closeEvent(event);
}
